# æå¡å¨éç½®
server.port=8082
server.servlet.context-path=/

# åºç¨éç½®
spring.application.name=ai-textbook-server

# ç¼ç éç½®
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# æ¥å¿éç½®
logging.level.com.goodlab=DEBUG
logging.level.org.springframework=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# JSONéç½®
spring.jackson.default-property-inclusion=NON_NULL
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
